#!/usr/bin/env python3
"""
Excel数据初始化脚本
将Excel文件中的住宿数据导入到系统中
"""

import pandas as pd
import requests
import json
from datetime import datetime
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API配置
BASE_URL = "http://localhost:8000/api/v1"
EXCEL_FILE = "新建 XLSX 工作表.xlsx"

# 认证token（需要先登录获取）
AUTH_TOKEN = None

class DataInitializer:
    def __init__(self, base_url: str, token: str = None):
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}" if token else ""
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 缓存已创建的数据
        self.departments_cache = {}
        self.dormitories_cache = {}
        self.residents_cache = {}
    
    def load_excel_data(self) -> pd.DataFrame:
        """加载Excel数据"""
        try:
            df = pd.read_excel(EXCEL_FILE, sheet_name='Sheet1')
            logger.info(f"成功加载Excel数据，共{len(df)}行")
            return df
        except Exception as e:
            logger.error(f"加载Excel文件失败: {e}")
            raise
    
    def create_department(self, dept_name: str) -> Optional[str]:
        """创建部门，返回部门ID"""
        if dept_name in self.departments_cache:
            return self.departments_cache[dept_name]
        
        try:
            data = {
                "name": dept_name,
                "description": f"{dept_name}部门",
                "is_active": True
            }
            
            response = self.session.post(f"{self.base_url}/departments/create", json=data)
            if response.status_code == 201:
                dept_data = response.json()
                dept_id = dept_data["id"]
                self.departments_cache[dept_name] = dept_id
                logger.info(f"创建部门成功: {dept_name} -> {dept_id}")
                return dept_id
            else:
                logger.error(f"创建部门失败: {dept_name}, 状态码: {response.status_code}, 响应: {response.text}")
                return None
        except Exception as e:
            logger.error(f"创建部门异常: {dept_name}, 错误: {e}")
            return None
    
    def create_dormitory(self, dorm_name: str, total_beds: int, dept_id: str = None) -> Optional[str]:
        """创建宿舍，返回宿舍ID"""
        if dorm_name in self.dormitories_cache:
            return self.dormitories_cache[dorm_name]
        
        try:
            data = {
                "name": dorm_name,
                "total_beds": total_beds,
                "description": f"宿舍地址: {dorm_name}",
                "department_id": dept_id,
                "is_active": True
            }
            
            response = self.session.post(f"{self.base_url}/dormitories/create", json=data)
            if response.status_code == 201:
                dorm_data = response.json()
                dorm_id = dorm_data["id"]
                self.dormitories_cache[dorm_name] = dorm_id
                logger.info(f"创建宿舍成功: {dorm_name} -> {dorm_id}")
                return dorm_id
            else:
                logger.error(f"创建宿舍失败: {dorm_name}, 状态码: {response.status_code}, 响应: {response.text}")
                return None
        except Exception as e:
            logger.error(f"创建宿舍异常: {dorm_name}, 错误: {e}")
            return None
    
    def create_resident(self, name: str, dept_id: str, employee_id: str = None) -> Optional[str]:
        """创建住户，返回住户ID"""
        cache_key = f"{name}_{dept_id}"
        if cache_key in self.residents_cache:
            return self.residents_cache[cache_key]
        
        try:
            data = {
                "name": name,
                "department_id": dept_id,
                "employee_id": employee_id,
                "is_active": True
            }
            
            response = self.session.post(f"{self.base_url}/residents/create", json=data)
            if response.status_code == 200:
                resident_data = response.json()
                resident_id = resident_data["id"]
                self.residents_cache[cache_key] = resident_id
                logger.info(f"创建住户成功: {name} -> {resident_id}")
                return resident_id
            else:
                logger.error(f"创建住户失败: {name}, 状态码: {response.status_code}, 响应: {response.text}")
                return None
        except Exception as e:
            logger.error(f"创建住户异常: {name}, 错误: {e}")
            return None
    
    def create_residence_record(self, resident_id: str, dormitory_id: str, 
                              project_group: str, check_in_date: str, notes: str = None) -> bool:
        """创建入住记录"""
        try:
            data = {
                "resident_id": resident_id,
                "dormitory_id": dormitory_id,
                "project_group": project_group,
                "check_in_date": check_in_date,
                "notes": notes
            }
            
            response = self.session.post(f"{self.base_url}/records/create", json=data)
            if response.status_code == 200:
                logger.info(f"创建入住记录成功: 住户{resident_id} -> 宿舍{dormitory_id}")
                return True
            else:
                logger.error(f"创建入住记录失败: 状态码: {response.status_code}, 响应: {response.text}")
                return False
        except Exception as e:
            logger.error(f"创建入住记录异常: {e}")
            return False
    
    def process_excel_data(self):
        """处理Excel数据并导入系统"""
        df = self.load_excel_data()
        
        # 统计信息
        total_records = 0
        success_records = 0
        failed_records = 0
        
        # 按宿舍分组处理数据
        dormitory_groups = {}
        
        for idx, row in df.iterrows():
            # 跳过空行
            if pd.isna(row['人员入住情况']) or not row['人员入住情况'].strip():
                continue
            
            # 提取数据
            dormitory_name = row['Unnamed: 0'] if not pd.isna(row['Unnamed: 0']) else "未知宿舍"
            bed_number = int(row['床号'])
            resident_name = row['人员入住情况'].strip()
            check_in_date = "2025-08-01"  # 统一入住时间
            department_name = row['部门'].strip() if not pd.isna(row['部门']) else "未知部门"
            client_name = row['客户'].strip() if not pd.isna(row['客户']) else "未知客户"
            ratio = row['报销分摊比例'] if not pd.isna(row['报销分摊比例']) else ""
            contract = row['合同编号'] if not pd.isna(row['合同编号']) else ""
            
            # 构建项目组信息
            project_group = f"{department_name}-{client_name}"
            
            # 构建备注信息
            notes_parts = []
            if ratio:
                notes_parts.append(f"报销分摊比例: {ratio}")
            if contract:
                notes_parts.append(f"合同编号: {contract}")
            notes = "; ".join(notes_parts) if notes_parts else None
            
            # 按宿舍分组
            if dormitory_name not in dormitory_groups:
                dormitory_groups[dormitory_name] = []
            
            dormitory_groups[dormitory_name].append({
                'bed_number': bed_number,
                'resident_name': resident_name,
                'department_name': department_name,
                'project_group': project_group,
                'check_in_date': check_in_date,
                'notes': notes
            })
        
        logger.info(f"解析Excel数据完成，共{len(dormitory_groups)}个宿舍")
        
        # 处理每个宿舍的数据
        for dormitory_name, residents in dormitory_groups.items():
            logger.info(f"处理宿舍: {dormitory_name}, 住户数: {len(residents)}")
            
            # 计算该宿舍的最大床位数
            max_bed = max(r['bed_number'] for r in residents)
            
            # 创建宿舍
            dormitory_id = self.create_dormitory(dormitory_name, max_bed)
            if not dormitory_id:
                logger.error(f"创建宿舍失败，跳过: {dormitory_name}")
                failed_records += len(residents)
                continue
            
            # 处理每个住户
            for resident_data in residents:
                total_records += 1
                
                # 创建部门
                dept_id = self.create_department(resident_data['department_name'])
                if not dept_id:
                    logger.error(f"创建部门失败，跳过住户: {resident_data['resident_name']}")
                    failed_records += 1
                    continue
                
                # 创建住户
                resident_id = self.create_resident(
                    resident_data['resident_name'], 
                    dept_id
                )
                if not resident_id:
                    logger.error(f"创建住户失败，跳过: {resident_data['resident_name']}")
                    failed_records += 1
                    continue
                
                # 创建入住记录
                success = self.create_residence_record(
                    resident_id,
                    dormitory_id,
                    resident_data['project_group'],
                    resident_data['check_in_date'],
                    resident_data['notes']
                )
                
                if success:
                    success_records += 1
                    logger.info(f"成功处理住户: {resident_data['resident_name']}")
                else:
                    failed_records += 1
                    logger.error(f"创建入住记录失败: {resident_data['resident_name']}")
        
        # 输出统计结果
        logger.info("=" * 50)
        logger.info("数据导入完成统计:")
        logger.info(f"总记录数: {total_records}")
        logger.info(f"成功导入: {success_records}")
        logger.info(f"失败记录: {failed_records}")
        logger.info(f"成功率: {success_records/total_records*100:.1f}%" if total_records > 0 else "0%")
        logger.info("=" * 50)

def main():
    """主函数"""
    print("Excel数据初始化脚本")
    print("=" * 50)
    
    # 检查是否需要认证token
    token = input("请输入认证Token（如果不需要认证请直接回车）: ").strip()
    if not token:
        token = None
    
    try:
        # 创建初始化器
        initializer = DataInitializer(BASE_URL, token)
        
        # 开始处理数据
        initializer.process_excel_data()
        
        print("\n数据初始化完成！")
        
    except Exception as e:
        logger.error(f"数据初始化失败: {e}")
        print(f"\n错误: {e}")

if __name__ == "__main__":
    main()
